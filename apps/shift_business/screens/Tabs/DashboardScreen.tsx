import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Button
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { BACKEND_URL } from '../../constants/env';
import ShiftDetailScreen from './ShiftDetailScreen';

interface Venue {
  id: number;
  name: string;
  description?: string;
  [key: string]: any;
}

interface Shift {
  id: number;
  title: string;
  description: string;
  start_time: string;
  end_time: string;
  hourly_rate: string;
  currency: string;
}

interface DetailScreenProps {
  venueId: number | null;
}


const Stack = createNativeStackNavigator();

export function DashboardStack({ venueId }: { venueId: number }) {
  return (
    <Stack.Navigator>
      <Stack.Screen name="Dashboard" options={{ title: 'Lieu' }}>
        {() => <DashboardScreen venueId={venueId} />}
      </Stack.Screen>
      <Stack.Screen
        name="ShiftDetail"
        component={ShiftDetailScreen}
        options={{ title: 'Détail du shift' }}
      />
      <Stack.Screen
        name="ShiftCreate"
        component={ShiftDetailScreen}
        options={{ title: 'Détail du shift' }}
      />
    </Stack.Navigator>
  );
}

export default function DashboardScreen({ venueId }: DetailScreenProps) {
    const [venue, setVenue] = useState<Venue | null>(null);
    const [shifts, setShifts] = useState<Shift[]>([]);
    const [loading, setLoading] = useState(false);
    const navigation = useNavigation();

    useEffect(() => {
        console.log("venueId : ", venueId);
        if (venueId === null) {
        setVenue(null);
        setShifts([]);
        return;
        }

        const fetchData = async () => {
        setLoading(true);
        try {
            const token = await AsyncStorage.getItem('supabase_jwt_token');
            const venueRes = await fetch(`${BACKEND_URL}/get_lieux_info?venue_id=${venueId}`, {
            headers: { Authorization: `Bearer ${token}` },
            });

            if (venueRes.ok) {
                const venueData = await venueRes.json();
                setVenue(venueData);
            }

            const shiftRes = await fetch(`${BACKEND_URL}/get_shifts_by_venue?venue_id=${venueId}`, {
            headers: { Authorization: `Bearer ${token}` },
            });

            if (shiftRes.ok) {
            const shiftData = await shiftRes.json();
            setShifts(shiftData);
            }
        } catch (error) {
            console.error('Erreur fetch data:', error);
            setVenue(null);
            setShifts([]);
        } finally {
            setLoading(false);
        }
        };

        fetchData();
    }, [venueId]);

    if (loading) {
        return (
        <View style={styles.centered}>
            <ActivityIndicator size="large" color="#007AFF" />
            <Text>Chargement du lieu...</Text>
        </View>
        );
    }

    if (!venue) {
        return (
        <View style={styles.centered}>
            <Text>Aucun lieu sélectionné.</Text>
        </View>
        );
    }

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <View style={{ padding: 20 }}>
                <Text style={styles.title}>{venue.name}</Text>
                <Text style={styles.subtitle}>{venue.type}</Text>
            </View>
            <FlatList
                data={shifts}
                keyExtractor={(item) => item.id.toString()}
                renderItem={({ item }) => (
                    <TouchableOpacity
                    style={styles.card}
                    onPress={() => navigation.navigate('ShiftDetail', { shift: item })}
                  >
                    <Text style={styles.cardTitle}>{item.title}</Text>
                    <Text style={styles.cardDescription}>{item.description}</Text>
                    
                    <View style={styles.row}>
                      <Text style={styles.label}>🕒</Text>
                      <Text style={styles.value}>
                        {new Date(item.start_time).toLocaleString()} - {new Date(item.end_time).toLocaleString()}
                      </Text>
                    </View>
                  
                    <View style={styles.row}>
                      <Text style={styles.label}>💸</Text>
                      <Text style={styles.value}>
                        {item.hourly_rate} {item.currency}/h
                      </Text>
                    </View>
                  </TouchableOpacity>
                )}
                contentContainerStyle={{ padding: 20 }}
            />
            <Button
                title="Créer un shift"
                onPress={() => navigation.navigate('ShiftCreate')}
            />
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 16,
    color: 'gray',
    marginTop: 5,
  },
  card: {
    backgroundColor: '#f9f9f9',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 3 },
    elevation: 3,
    borderWidth: 1,
    borderColor: '#eee',
  },
  
  cardTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 6,
    color: '#333',
  },
  
  cardDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  
  label: {
    fontSize: 14,
    color: '#555',
    marginRight: 8,
  },
  
  value: {
    fontSize: 14,
    color: '#333',
    flexShrink: 1,
  },
  
});